<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>API Connection Test</h1>
    
    <div>
        <button id="test-get">Test GET</button>
        <button id="test-post">Test POST</button>
        <button id="test-options">Test OPTIONS</button>
    </div>
    
    <h2>Status: <span id="status">Not tested yet</span></h2>
    
    <h2>Response:</h2>
    <pre id="response">Click a button to test the API connection</pre>
    
    <script>
        // Function to update status
        function updateStatus(success, message) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = success ? 'success' : 'error';
        }
        
        // Function to test API connection
        async function testConnection(method) {
            const responseElement = document.getElementById('response');
            responseElement.textContent = 'Loading...';
            updateStatus(true, 'Testing...');
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };
                
                // Add body for POST requests
                if (method === 'POST') {
                    options.body = JSON.stringify({
                        test: true,
                        timestamp: new Date().toISOString()
                    });
                }
                
                const response = await fetch('http://localhost/project-react-resto/api/test-connection.php', options);
                const result = await response.json();
                
                responseElement.textContent = JSON.stringify(result, null, 2);
                updateStatus(true, 'Connection successful!');
            } catch (error) {
                responseElement.textContent = 'Error: ' + error.message;
                updateStatus(false, 'Connection failed: ' + error.message);
                console.error('Connection test error:', error);
            }
        }
        
        // Add event listeners to buttons
        document.getElementById('test-get').addEventListener('click', () => testConnection('GET'));
        document.getElementById('test-post').addEventListener('click', () => testConnection('POST'));
        document.getElementById('test-options').addEventListener('click', () => testConnection('OPTIONS'));
    </script>
</body>
</html>
