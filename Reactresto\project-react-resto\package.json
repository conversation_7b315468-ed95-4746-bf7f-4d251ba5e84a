{"name": "project-react-resto", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.9.0", "bootstrap": "^5.3.6", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-router-dom": "^6.20.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.7"}}