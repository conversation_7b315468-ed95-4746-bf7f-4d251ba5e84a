<?php
// Header untuk CORS dan JSON
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// <PERSON><PERSON> request adalah OPTIONS, hentikan di sini (pre-flight request)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Pastikan metode request adalah POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode([
        'success' => false,
        'message' => 'Metode tidak diizinkan. Gunakan POST.'
    ]);
    exit;
}

// Ambil data dari request body
$data = json_decode(file_get_contents("php://input"), true);

// Validasi data
if (!isset($data['nama_kategori']) || empty($data['nama_kategori'])) {
    http_response_code(400); // Bad Request
    echo json_encode([
        'success' => false,
        'message' => 'Nama kategori tidak boleh kosong'
    ]);
    exit;
}

// Koneksi ke database
require_once 'config.php';

try {
    // Buat koneksi ke database
    $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Siapkan data untuk disimpan
    $nama_kategori = $data['nama_kategori'];
    $keterangan = isset($data['keterangan']) ? $data['keterangan'] : '';
    
    // Query untuk menambahkan kategori baru
    $stmt = $conn->prepare("INSERT INTO kategori (nama_kategori, keterangan) VALUES (:nama_kategori, :keterangan)");
    $stmt->bindParam(':nama_kategori', $nama_kategori);
    $stmt->bindParam(':keterangan', $keterangan);
    $stmt->execute();
    
    // Ambil ID kategori yang baru ditambahkan
    $newId = $conn->lastInsertId();
    
    // Kembalikan response sukses
    echo json_encode([
        'success' => true,
        'data' => [
            'id' => $newId,
            'nama_kategori' => $nama_kategori,
            'keterangan' => $keterangan
        ],
        'message' => 'Kategori berhasil ditambahkan'
    ]);
    
} catch (PDOException $e) {
    // Kembalikan response error
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Gagal menambahkan kategori: ' . $e->getMessage()
    ]);
}