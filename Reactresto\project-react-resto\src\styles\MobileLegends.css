/* Mobile Legends Theme CSS */

/* Hero Card Animations */
.hero-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.2), transparent);
  transition: 0.8s;
}

.hero-card:hover::before {
  left: 100%;
}

.hero-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(251, 191, 36, 0.6);
  box-shadow:
    0 20px 40px rgba(30, 58, 138, 0.3),
    0 0 30px rgba(251, 191, 36, 0.4);
}

/* Battle <PERSON>ton Styles */
.battle-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.battle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: 0.6s;
}

.battle-btn:hover::before {
  left: 100%;
}

.battle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
}

/* Skill Card Styles */
.skill-card {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin: 0.5rem 0;
  transition: all 0.3s ease;
}

.skill-card:hover {
  border-color: rgba(16, 185, 129, 0.6);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%);
  transform: translateX(5px);
}

/* Player Stats */
.player-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.stat-item {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  color: #94a3b8;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Hero Role Badges */
.role-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-tank {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.role-fighter {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.role-assassin {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-mage {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.role-marksman {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.role-support {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* Battle Arena Background */
.battle-arena {
  background:
    radial-gradient(circle at 25% 25%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  min-height: 100vh;
  position: relative;
}

.battle-arena::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  animation: battleGlow 8s ease-in-out infinite alternate;
}

@keyframes battleGlow {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* Legendary Text Effect */
.legendary-text {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #ef4444, #8b5cf6, #3b82f6);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryShine 3s ease-in-out infinite;
  font-weight: 800;
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
}

@keyframes legendaryShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Mobile Legends Loading Animation */
.ml-loading {
  display: inline-block;
  width: 60px;
  height: 60px;
  border: 3px solid rgba(251, 191, 36, 0.3);
  border-radius: 50%;
  border-top-color: #fbbf24;
  animation: mlSpin 1s ease-in-out infinite;
}

@keyframes mlSpin {
  to { transform: rotate(360deg); }
}

/* Hero Selection Grid */
.hero-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .player-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-number {
    font-size: 2rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Content Card Enhancement */
.content-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  box-shadow:
    0 10px 30px rgba(59, 130, 246, 0.15),
    0 0 20px rgba(251, 191, 36, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  pointer-events: none;
}

.content-card:hover {
  transform: translateY(-2px);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow:
    0 15px 40px rgba(30, 58, 138, 0.3),
    0 0 30px rgba(251, 191, 36, 0.2);
}

/* Table Enhancement */
.table {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.table thead th {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: white;
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 1rem;
}

.table tbody td {
  background: rgba(248, 250, 252, 0.8);
  color: #1e293b;
  border-color: rgba(59, 130, 246, 0.2);
  padding: 1rem;
  vertical-align: middle;
}

.table tbody tr:hover td {
  background: rgba(59, 130, 246, 0.1);
  color: #1e293b;
}

/* Button Enhancements */
.btn-custom.btn-primary-custom {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border: 2px solid rgba(251, 191, 36, 0.3);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.btn-custom.btn-primary-custom:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-color: rgba(251, 191, 36, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(30, 58, 138, 0.4);
}

/* Alert Enhancement */
.alert {
  border-radius: 12px;
  border: none;
  backdrop-filter: blur(10px);
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.9) 0%, rgba(5, 150, 105, 0.9) 100%);
  color: white;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
  color: white;
}

.alert-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%);
  color: white;
}

/* Form Enhancement */
.form-control {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #1e293b;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  color: #1e293b;
}

.form-control::placeholder {
  color: #64748b;
}

.form-label {
  color: #1e293b;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Modal Enhancement */
.modal-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.modal-header {
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.modal-title {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-footer {
  border-top: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(248, 250, 252, 0.8);
  border-radius: 0 0 16px 16px;
}

.modal-body {
  color: #1e293b;
}

/* Spinner Enhancement */
.spinner-border {
  color: #fbbf24;
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
}

/* Card Enhancement */
.card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.card-body {
  color: #1e293b;
}

/* Text Colors */
.text-muted {
  color: #64748b !important;
}

.text-white {
  color: #ffffff !important;
}

.text-dark {
  color: #1e293b !important;
}

/* Hero Glow Effect */
.hero-glow {
  animation: heroGlow 2s ease-in-out infinite alternate;
}

@keyframes heroGlow {
  from {
    text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  }
  to {
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.8), 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

/* Additional Component Styles */
.hero-selection-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.hero-selection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.hero-selection-item {
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.hero-selection-item:hover {
  transform: scale(1.05);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.player-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.player-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.player-stats-card {
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

/* Skill Badge */
.skill-badge {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Hero Filter Container */
.hero-filter-container {
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

/* Battle Arena Enhancements */
.battle-arena {
  background:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 0.95) 100%);
  min-height: 100vh;
  position: relative;
  border-radius: 16px;
  padding: 2rem;
}

.battle-arena::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.03) 0%, transparent 50%);
  animation: battleGlow 8s ease-in-out infinite alternate;
  border-radius: 16px;
}

/* Loading Animation for Bright Theme */
.ml-loading {
  display: inline-block;
  width: 60px;
  height: 60px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: mlSpin 1s ease-in-out infinite;
}

/* Menu Title Enhancement */
.menu-title {
  background: linear-gradient(45deg, #3b82f6, #60a5fa, #fbbf24, #f59e0b);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryShine 3s ease-in-out infinite;
  font-weight: 800;
  font-size: 2.5rem;
}

/* Button Hover Effects */
.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.btn-warning:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
}

/* Progress Bar Enhancement */
.progress {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  border-radius: 10px;
  transition: all 0.3s ease;
}

/* Badge Enhancements */
.badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 0.5rem 0.75rem;
}

/* Navigation Enhancements */
.nav-header {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: white;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ===== HERO MANAGEMENT PAGE STYLES ===== */

/* Hero Management Page Container */
.hero-management-page {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(255, 255, 255, 0.98) 50%,
    rgba(240, 249, 255, 0.95) 100%);
  position: relative;
  overflow-x: hidden;
}

.hero-management-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(251, 191, 36, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.hero-management-page > .container-fluid {
  position: relative;
  z-index: 1;
}

/* Hero Header Section */
.hero-header-section {
  padding: 3rem 0 2rem;
  margin-bottom: 2rem;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(251, 191, 36, 0.05) 100%);
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
}

.hero-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.hero-title-container {
  flex: 1;
  text-align: center;
}

.hero-main-title {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8, #fbbf24, #f59e0b);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryShine 4s ease-in-out infinite;
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
  letter-spacing: 2px;
}

.title-icon {
  display: inline-block;
  animation: heroGlow 2s ease-in-out infinite alternate;
  margin: 0 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 500;
  letter-spacing: 1px;
  margin: 0;
}

.hero-admin-actions {
  flex-shrink: 0;
}

.add-hero-btn {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.add-hero-btn:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
}

.btn-icon {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

/* Alert Container */
.alert-container {
  margin-bottom: 2rem;
}

.alert-container .alert {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.alert-icon {
  font-size: 1.2rem;
}

/* Hero Statistics Bar */
.hero-stats-bar {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  padding: 0.5rem 1rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #3b82f6;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-divider {
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(59, 130, 246, 0.3) 50%,
    transparent 100%);
}

/* Hero Filter Section */
.hero-filter-section {
  margin-bottom: 3rem;
}

.hero-filter-wrapper {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(15px);
}

.filter-header {
  text-align: center;
  margin-bottom: 2rem;
}

.filter-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.filter-icon {
  font-size: 1.3rem;
}

.filter-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Search Section */
.search-section {
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: #64748b;
  z-index: 2;
}

.search-input {
  padding: 1rem 1rem 1rem 3rem;
  border-radius: 12px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.clear-search-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  padding: 0.25rem 0.5rem;
  font-size: 1rem;
  color: #64748b;
  text-decoration: none;
}

/* Role Filter Section */
.role-filter-section {
  text-align: center;
}

.filter-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.label-icon {
  font-size: 1.1rem;
}

.role-buttons-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.role-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid rgba(59, 130, 246, 0.3);
  background: rgba(255, 255, 255, 0.8);
  min-width: 120px;
  justify-content: center;
}

.role-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.role-btn.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #1d4ed8;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.role-icon {
  font-size: 1.1rem;
}

/* Sort and Actions Section */
.sort-actions-section {
  display: flex;
  justify-content: center;
  align-items: end;
  gap: 2rem;
  flex-wrap: wrap;
}

.sort-container {
  flex: 1;
  max-width: 250px;
}

.sort-select {
  padding: 0.75rem;
  border-radius: 12px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.sort-select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.actions-container {
  flex-shrink: 0;
}

.reset-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(108, 117, 125, 0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
}

/* Hero Grid Section */
.hero-grid-section {
  margin-bottom: 3rem;
}

.hero-grid-container {
  padding: 1rem 0;
}

.hero-grid {
  margin: 0 -0.75rem;
}

.hero-col {
  padding: 0 0.75rem;
}

/* No Heroes Container */
.no-heroes-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 3rem;
}

.no-heroes-content {
  text-align: center;
  max-width: 500px;
}

.no-heroes-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.no-heroes-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.no-heroes-text {
  color: #64748b;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.reset-filters-btn {
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.reset-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

/* Responsive Improvements */
@media (max-width: 1200px) {
  .hero-main-title {
    font-size: 3rem;
  }

  .stats-container {
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 992px) {
  .hero-header-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-main-title {
    font-size: 2.5rem;
  }

  .role-buttons-container {
    gap: 0.5rem;
  }

  .role-btn {
    min-width: 100px;
    padding: 0.6rem 1rem;
  }
}

@media (max-width: 768px) {
  .hero-header-section {
    padding: 2rem 1rem;
  }

  .hero-main-title {
    font-size: 2rem;
  }

  .title-icon {
    margin: 0 0.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-filter-wrapper {
    padding: 1.5rem;
  }

  .stats-container {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .sort-actions-section {
    flex-direction: column;
    gap: 1rem;
  }

  .sort-container {
    max-width: 100%;
  }

  .role-buttons-container {
    justify-content: center;
  }

  .role-btn {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
  }
}

@media (max-width: 576px) {
  .hero-main-title {
    font-size: 1.8rem;
  }

  .stats-container {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-divider {
    width: 40px;
    height: 2px;
  }

  .role-buttons-container {
    flex-direction: column;
    align-items: center;
  }

  .role-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* ===== ENHANCED HERO CARD STYLES ===== */

/* Hero Card Container */
.hero-card-enhanced {
  border: none;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.hero-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 60px rgba(59, 130, 246, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Hero Image Section */
.hero-image-container {
  position: relative;
  overflow: hidden;
  height: 220px;
}

.hero-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
}

.hero-card-enhanced:hover .hero-image {
  transform: scale(1.1);
}

.hero-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(251, 191, 36, 0.1) 100%
  );
  opacity: 0;
  transition: all 0.3s ease;
}

.hero-card-enhanced:hover .hero-image-overlay {
  opacity: 1;
}

/* Hero Badges */
.hero-badges {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 2;
}

.role-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.8rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-tank { background: rgba(108, 117, 125, 0.9); color: white; }
.role-fighter { background: rgba(220, 53, 69, 0.9); color: white; }
.role-assassin { background: rgba(33, 37, 41, 0.9); color: white; }
.role-mage { background: rgba(13, 110, 253, 0.9); color: white; }
.role-marksman { background: rgba(255, 193, 7, 0.9); color: #1e293b; }
.role-support { background: rgba(25, 135, 84, 0.9); color: white; }

.legendary-badge {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e293b;
  padding: 0.5rem 0.75rem;
  border-radius: 12px;
  font-weight: 700;
  font-size: 0.8rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(251, 191, 36, 0.3);
  animation: legendaryPulse 2s ease-in-out infinite;
}

@keyframes legendaryPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.role-icon, .legendary-icon {
  font-size: 0.9rem;
}

/* Hero Content */
.hero-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: calc(100% - 220px);
}

/* Hero Header */
.hero-header {
  text-align: center;
  margin-bottom: 0.5rem;
}

.hero-name {
  font-size: 1.4rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.4;
  margin: 0;
}

/* Hero Stats Section */
.hero-stats-section {
  margin: 0.5rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.1rem;
}

/* Hero Skills Section */
.hero-skills-section {
  margin: 0.5rem 0;
}

.skills-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.9rem;
}

.skills-icon {
  font-size: 1rem;
}

.skills-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skill-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(251, 191, 36, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(251, 191, 36, 0.1);
  transition: all 0.3s ease;
}

.skill-item:hover {
  background: rgba(251, 191, 36, 0.1);
  transform: translateX(4px);
}

.skill-dot {
  width: 6px;
  height: 6px;
  background: #fbbf24;
  border-radius: 50%;
  flex-shrink: 0;
}

.skill-name {
  font-size: 0.8rem;
  color: #1e293b;
  font-weight: 500;
}

/* Hero Actions */
.hero-actions {
  margin-top: auto;
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.select-hero-btn {
  flex: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
}

.select-hero-btn:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-icon {
  font-size: 1rem;
}

.admin-actions {
  display: flex;
  gap: 0.5rem;
}

.admin-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
  border-width: 2px;
}

.admin-btn:hover {
  transform: translateY(-2px);
}

.edit-btn:hover {
  background: #6c757d;
  border-color: #6c757d;
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.delete-btn:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.admin-icon {
  font-size: 1rem;
}

/* Responsive Hero Card */
@media (max-width: 768px) {
  .hero-image-container {
    height: 180px;
  }

  .hero-content {
    padding: 1.25rem;
    height: calc(100% - 180px);
  }

  .hero-name {
    font-size: 1.2rem;
  }

  .stats-grid {
    gap: 0.5rem;
  }

  .stat-item {
    padding: 0.5rem;
  }

  .stat-value {
    font-size: 1rem;
  }

  .select-hero-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .admin-btn {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 576px) {
  .hero-image-container {
    height: 160px;
  }

  .hero-content {
    padding: 1rem;
    gap: 0.75rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .stat-item {
    justify-content: center;
  }

  .skills-container {
    gap: 0.4rem;
  }

  .skill-item {
    padding: 0.4rem 0.6rem;
  }
}
